import json
import uuid
import chromadb
from tqdm import tqdm
import requests

chromadb_client = chromadb.PersistentClient(path="__chromadb__")
chromadb_graph_collection = chromadb_client.get_or_create_collection(name="graph")

file_path = r"E:\data\Internship\MindMap RAG\NLP then LLM\2. KG\archive\KG5_with_thoughts\OPT4\outputs\run_20250824_235921\enriched_mindmap_opt1.json"
data = json.load(open(file_path))

def ollama_embed(uri, json_payload, auth_type=None, auth_key=None):  
    headers = None
    if auth_type == "bearer_token":
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {auth_key}"}
    elif auth_type == "api_key":
        if auth_key is None:
            raise ValueError(f"auth_type is {auth_type}, but auth_key is null")
        headers = {"Content-Type": "application/json", "api-key": f"{auth_key}"}
    response = requests.post(uri, json=json_payload, headers=headers)
    return response.json()

def get_embeds(sentence):
    return ollama_embed(f"https://dev.assisto.tech/ollama/api/embed", {'model': 'bge-m3', 'input': sentence}, None, None)['embeddings']

def get_kg_segments(data):
    segments = []
    for topic in data['topics']:
        for segment in data['topics'][topic]['linked_content']:
            segment['topic'] = topic
            segments.append(segment)
    return segments

def ingest_graph(segments, batch_size=16):
    for i in tqdm(range(0, len(segments), batch_size)):
        batch = segments[i:i+batch_size]
        batch = [json.dumps(j) for j in batch]
        embeddings = get_embeds(batch)
        chromadb_graph_collection.add(
            ids=[f'{str(uuid.uuid4())}' for _ in range(len(batch))],
            documents=batch,
            embeddings=embeddings,
        )

segments = get_kg_segments(data)
ingest_graph(segments, batch_size=16)

query = "What is the HCAI framework?"

def query_graph(query):
    results = chromadb_graph_collection.query(
        query_embeddings=get_embeds(query),
        )
    return [json.loads(r) for r in results['documents'][0]]
            
query_graph(query)



import json
import requests
from jinja2 import Template

def ollama_llm(uri, json_payload, auth_type=None, auth_key=None, stream=True):  
    headers = None
    if auth_type == "bearer_token":
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {auth_key}"}
    elif auth_type == "api_key":
        if auth_key is None:
            raise ValueError(f"auth_type is {auth_type}, but auth_key is null")
        headers = {"Content-Type": "application/json", "api-key": f"{auth_key}"}
    json_payload['stream'] = stream
    with requests.post(uri, json=json_payload, headers=headers, stream=stream) as response:
        if response.status_code == 200:
            for token in response.iter_lines(decode_unicode=True):
                data = json.loads(token)
                yield json.dumps({"response": data['response']}) + "\n" 
        else:
            yield json.dumps({"error": response.status_code, "message": response.text}) + "\n"

def get_llm_completion(prompt, model):
    for token in ollama_llm(
        f"https://dev.assisto.tech/ollama/api/generate",
        {
            "model": model,
            "prompt": prompt
            }
                            ):
        token = json.loads(token)['response']
        yield token

templates = {  
    'qwen': Template("""<|im_start|>system
{{ instruction }}
Context information:
{% for segment in context %}
- {{ segment.content }} (Topic: {{ segment.topic }})
{% endfor %}
<|im_end|>
{% for message in history %}
<|im_start|>{{ message.role }}
{{ message.content }}<|im_end|>
{% endfor %}
<|im_start|>assistant
""")
}

def format_context(result):
    """Format the graph query result into a readable context string"""
    context_segments = result['direct_results'] + result['subgraph_segments']
    return context_segments

history = []

while True:
    try:
        user_input = input("User: ")
        if user_input.lower() in ['exit', 'quit']:
            break
            
        # Get relevant context from knowledge graph
        graph_result = query_graph_with_subgraph(user_input)
        context_segments = format_context(graph_result)
        
        # Prepare the prompt with context
        prompt = templates['qwen'].render(
            instruction='Answer the question based on the context below. If unsure, say so.',
            context=context_segments,
            history=history
        )
        
        # Generate response
        output = ''
        for token in get_llm_completion(prompt, 'qwen3:4b'):
            print(token, end='', flush=True)
            output += token
        
        print()
        history.append({'role': 'user', 'content': user_input})
        history.append({'role': 'assistant', 'content': output})
        
    except KeyboardInterrupt:
        print("\nExiting...")
        break
